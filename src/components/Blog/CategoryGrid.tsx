"use client";

import React from "react";
import * as Icon from "react-feather";
import <PERSON> from "next/link";
import { TCategoryData } from '@/types/blog/category';

interface CategoryGridProps {
  categories: TCategoryData[];
}

const CategoryGrid: React.FC<CategoryGridProps> = ({ categories }) => {
  const renderIcon = (iconString: string) => {
    // If it's an SVG string, render it directly
    if (iconString.includes('<svg')) {
      return (
        <div
          className="category-icon"
          dangerouslySetInnerHTML={{ __html: iconString }}
        />
      );
    }

    // If it's a class name, render a default icon
    return <Icon.Folder className="category-icon" />;
  };

  return (
    <>
      <div className="blog-area ptb-80">
        <div className="container">
          <div className="row justify-content-center">
            {categories.map((category) => (
              <div key={category.id} className="col-lg-4 col-md-6">
                <div className="single-blog-post">
                  <div className="blog-image">
                    <Link href={`/categories/${category.slug}`}>
                      <div className="category-icon-wrapper">
                        {renderIcon(category.icon)}
                      </div>
                    </Link>

                    <div className="date">
                      <Icon.Tag /> {category.posts_count || 0} posts
                    </div>
                  </div>

                  <div className="blog-post-content">
                    <h3>
                      <Link href={`/categories/${category.slug}`}>
                        {category.name}
                      </Link>
                    </h3>

                    {category.parent && (
                      <span>
                        Parent:{" "}
                        <Link href={`/categories/${category.parent.slug}`}>
                          {category.parent.name}
                        </Link>
                      </span>
                    )}

                    {category.description && (
                      <p dangerouslySetInnerHTML={{ __html: category.description }} />
                    )}

                    <Link
                      href={`/categories/${category.slug}`}
                      className="read-more-btn"
                    >
                      View Posts <Icon.ArrowRight />
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <style jsx>{`
        .category-icon-wrapper {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 200px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 10px;
          transition: all 0.3s ease;
        }

        .category-icon-wrapper:hover {
          transform: scale(1.05);
        }

        .category-icon-wrapper :global(.category-icon) {
          width: 60px;
          height: 60px;
          color: white;
        }

        .category-icon-wrapper :global(svg) {
          width: 60px;
          height: 60px;
          color: white;
        }

        .single-blog-post {
          margin-bottom: 30px;
        }

        .blog-image {
          position: relative;
          overflow: hidden;
          border-radius: 10px;
        }

        .date {
          position: absolute;
          bottom: 20px;
          left: 20px;
          background: rgba(255, 255, 255, 0.9);
          color: #333;
          padding: 8px 15px;
          border-radius: 5px;
          font-size: 14px;
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 5px;
        }

        .blog-post-content {
          padding: 25px 0;
        }

        .blog-post-content h3 {
          margin-bottom: 15px;
          font-size: 22px;
          font-weight: 600;
          line-height: 1.3;
        }

        .blog-post-content h3 a {
          color: #333;
          text-decoration: none;
          transition: color 0.3s ease;
        }

        .blog-post-content h3 a:hover {
          color: #007bff;
        }

        .blog-post-content span {
          color: #666;
          font-size: 14px;
          margin-bottom: 15px;
          display: block;
        }

        .blog-post-content span a {
          color: #007bff;
          text-decoration: none;
        }

        .blog-post-content span a:hover {
          text-decoration: underline;
        }

        .blog-post-content p {
          color: #666;
          line-height: 1.6;
          margin-bottom: 20px;
        }

        .read-more-btn {
          color: #007bff;
          text-decoration: none;
          font-weight: 600;
          display: inline-flex;
          align-items: center;
          gap: 5px;
          transition: color 0.3s ease;
        }

        .read-more-btn:hover {
          color: #0056b3;
        }

        .read-more-btn :global(svg) {
          width: 16px;
          height: 16px;
        }
      `}</style>
    </>
  );
};

export default CategoryGrid;
