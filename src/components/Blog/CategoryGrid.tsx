"use client";

import React from "react";
import * as Icon from "react-feather";
import Link from "next/link";
import { TCategoryData } from '@/types/blog/category';

interface CategoryGridProps {
  categories: TCategoryData[];
}

const CategoryGrid: React.FC<CategoryGridProps> = ({ categories }) => {
  console.log({categories});
  const renderIcon = (iconString: string) => {
    if (iconString.includes('<svg')) {
      return (
        <div
          className="category-icon"
          dangerouslySetInnerHTML={{ __html: iconString }}
        />
      );
    }

    return <Icon.Folder className="category-icon" />;
  };

  return (
    <>
      <div className="blog-area ptb-80">
        <div className="container">
          <div className="row justify-content-center">
            {categories.map((category) => (
              <div key={category.id} className="col-lg-4 col-md-6">
                <div className="single-blog-post">
                  <div className="blog-image category-image">
                    <Link href={`/categories/${category.slug}`}>
                      <div className="category-icon-wrapper">
                        {renderIcon(category.icon)}
                      </div>
                    </Link>

                    <div className="date">
                      <Icon.Tag /> {category.posts_count || 0} posts
                    </div>
                  </div>

                  <div className="blog-post-content">
                    <h3>
                      <Link href={`/categories/${category.slug}`}>
                        {category.name}
                      </Link>
                    </h3>

                    {category.parent && (
                      <span>
                        Parent:{" "}
                        <Link href={`/categories/${category.parent.slug}`}>
                          {category.parent.name}
                        </Link>
                      </span>
                    )}

                    {category.description && (
                      <p dangerouslySetInnerHTML={{ __html: category.description }} />
                    )}

                    <Link
                      href={`/categories/${category.slug}`}
                      className="read-more-btn"
                    >
                      View Posts <Icon.ArrowRight />
                    </Link>
                  </div>
                </div>
              </div>
            ))}

            {/* Pagination */}
            <div className="col-lg-12 col-md-12">
              <div className="pagination-area">
                <nav aria-label="Page navigation">
                  <ul className="pagination justify-content-center">
                    <li className="page-item">
                      <a className="page-link" href="#">
                        Prev
                      </a>
                    </li>

                    <li className="page-item active">
                      <a className="page-link" href="#">
                        1
                      </a>
                    </li>

                    <li className="page-item">
                      <a className="page-link" href="#">
                        2
                      </a>
                    </li>

                    <li className="page-item">
                      <a className="page-link" href="#">
                        3
                      </a>
                    </li>

                    <li className="page-item">
                      <a className="page-link" href="#">
                        Next
                      </a>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CategoryGrid;
