'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import * as Icon from 'react-feather';
import { TCategoryData } from '@/types/blog/category';

interface CategoryGridProps {
  categories: TCategoryData[];
}

const CategoryGrid: React.FC<CategoryGridProps> = ({ categories }) => {
  const renderIcon = (iconString: string) => {
    // If it's an SVG string, render it directly
    if (iconString.includes('<svg')) {
      return (
        <div 
          className="category-icon"
          dangerouslySetInnerHTML={{ __html: iconString }}
        />
      );
    }
    
    // If it's a class name, render a default icon
    return <Icon.Folder className="category-icon" />;
  };

  return (
    <div className="blog-area ptb-80">
      <div className="container">
        <div className="row justify-content-center">
          {categories.map((category) => (
            <div key={category.id} className="col-lg-4 col-md-6 mb-4">
              <div className="single-category-card">
                <div className="category-header">
                  <div className="category-icon-wrapper">
                    {renderIcon(category.icon)}
                  </div>
                  <div className="category-info">
                    <h3>
                      <Link href={`/categories/${category.slug}`}>
                        {category.name}
                      </Link>
                    </h3>
                    {category.posts_count !== undefined && (
                      <span className="posts-count">
                        {category.posts_count} {category.posts_count === 1 ? 'post' : 'posts'}
                      </span>
                    )}
                  </div>
                </div>

                {category.description && (
                  <div className="category-description">
                    <p dangerouslySetInnerHTML={{ __html: category.description }} />
                  </div>
                )}

                {category.parent && (
                  <div className="category-parent">
                    <span className="parent-label">Parent: </span>
                    <Link href={`/categories/${category.parent.slug}`} className="parent-link">
                      {category.parent.name}
                    </Link>
                  </div>
                )}

                <div className="category-footer">
                  <Link
                    href={`/categories/${category.slug}`}
                    className="view-category-btn"
                  >
                    View Posts <Icon.ArrowRight />
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <style jsx>{`
        .single-category-card {
          background: #fff;
          border-radius: 10px;
          padding: 30px;
          box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease-in-out;
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .single-category-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        }

        .category-header {
          display: flex;
          align-items: flex-start;
          margin-bottom: 20px;
        }

        .category-icon-wrapper {
          margin-right: 15px;
          flex-shrink: 0;
        }

        .category-icon-wrapper :global(.category-icon) {
          width: 40px;
          height: 40px;
          color: #007bff;
        }

        .category-icon-wrapper :global(svg) {
          width: 40px;
          height: 40px;
          color: #007bff;
        }

        .category-info {
          flex: 1;
        }

        .category-info h3 {
          margin: 0 0 5px 0;
          font-size: 20px;
          font-weight: 600;
        }

        .category-info h3 a {
          color: #333;
          text-decoration: none;
          transition: color 0.3s ease;
        }

        .category-info h3 a:hover {
          color: #007bff;
        }

        .posts-count {
          color: #666;
          font-size: 14px;
        }

        .category-description {
          margin-bottom: 20px;
          flex: 1;
        }

        .category-description p {
          color: #666;
          line-height: 1.6;
          margin: 0;
        }

        .category-parent {
          margin-bottom: 20px;
          padding: 10px;
          background: #f8f9fa;
          border-radius: 5px;
        }

        .parent-label {
          font-weight: 600;
          color: #333;
        }

        .parent-link {
          color: #007bff;
          text-decoration: none;
        }

        .parent-link:hover {
          text-decoration: underline;
        }

        .category-footer {
          margin-top: auto;
        }

        .view-category-btn {
          display: inline-flex;
          align-items: center;
          color: #007bff;
          text-decoration: none;
          font-weight: 600;
          transition: color 0.3s ease;
        }

        .view-category-btn:hover {
          color: #0056b3;
        }

        .view-category-btn :global(svg) {
          margin-left: 5px;
          width: 16px;
          height: 16px;
        }
      `}</style>
    </div>
  );
};

export default CategoryGrid;
