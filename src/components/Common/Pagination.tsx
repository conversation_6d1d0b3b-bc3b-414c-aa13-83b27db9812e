'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';
import * as Icon from 'react-feather';

interface PaginationProps {
  currentPage: number;
  lastPage: number;
  total: number;
  perPage: number;
  from: number;
  to: number;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  lastPage,
  total,
  perPage,
  from,
  to
}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const createPageURL = (pageNumber: number) => {
    const params = new URLSearchParams(searchParams);
    params.set('page', pageNumber.toString());
    return `${pathname}?${params.toString()}`;
  };

  const getVisiblePages = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (
      let i = Math.max(2, currentPage - delta);
      i <= Math.min(lastPage - 1, currentPage + delta);
      i++
    ) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < lastPage - 1) {
      rangeWithDots.push('...', lastPage);
    } else if (lastPage > 1) {
      rangeWithDots.push(lastPage);
    }

    return rangeWithDots;
  };

  if (lastPage <= 1) {
    return null;
  }

  const visiblePages = getVisiblePages();

  return (
    <div className="pagination-area">
      <div className="container">
        <div className="row">
          <div className="col-lg-12">
            <div className="pagination-info">
              <p>
                Showing {from} to {to} of {total} results
              </p>
            </div>
            
            <nav className="pagination-nav">
              <ul className="pagination">
                {/* Previous Button */}
                <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                  {currentPage === 1 ? (
                    <span className="page-link">
                      <Icon.ChevronLeft />
                    </span>
                  ) : (
                    <Link href={createPageURL(currentPage - 1)} className="page-link">
                      <Icon.ChevronLeft />
                    </Link>
                  )}
                </li>

                {/* Page Numbers */}
                {visiblePages.map((page, index) => (
                  <li key={index} className={`page-item ${page === currentPage ? 'active' : ''}`}>
                    {page === '...' ? (
                      <span className="page-link dots">...</span>
                    ) : (
                      <Link 
                        href={createPageURL(page as number)} 
                        className="page-link"
                      >
                        {page}
                      </Link>
                    )}
                  </li>
                ))}

                {/* Next Button */}
                <li className={`page-item ${currentPage === lastPage ? 'disabled' : ''}`}>
                  {currentPage === lastPage ? (
                    <span className="page-link">
                      <Icon.ChevronRight />
                    </span>
                  ) : (
                    <Link href={createPageURL(currentPage + 1)} className="page-link">
                      <Icon.ChevronRight />
                    </Link>
                  )}
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>

      <style jsx>{`
        .pagination-area {
          padding: 40px 0;
        }

        .pagination-info {
          text-align: center;
          margin-bottom: 20px;
        }

        .pagination-info p {
          color: #666;
          margin: 0;
        }

        .pagination-nav {
          display: flex;
          justify-content: center;
        }

        .pagination {
          display: flex;
          list-style: none;
          margin: 0;
          padding: 0;
          gap: 5px;
        }

        .page-item {
          display: flex;
        }

        .page-link {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 10px 15px;
          color: #333;
          text-decoration: none;
          border: 1px solid #ddd;
          border-radius: 5px;
          transition: all 0.3s ease;
          min-width: 45px;
          height: 45px;
        }

        .page-link:hover {
          background-color: #007bff;
          color: white;
          border-color: #007bff;
        }

        .page-item.active .page-link {
          background-color: #007bff;
          color: white;
          border-color: #007bff;
        }

        .page-item.disabled .page-link {
          color: #ccc;
          cursor: not-allowed;
          background-color: #f8f9fa;
        }

        .page-item.disabled .page-link:hover {
          background-color: #f8f9fa;
          color: #ccc;
          border-color: #ddd;
        }

        .dots {
          cursor: default;
        }

        .dots:hover {
          background-color: transparent !important;
          color: #333 !important;
          border-color: #ddd !important;
        }

        @media (max-width: 768px) {
          .pagination {
            gap: 2px;
          }

          .page-link {
            padding: 8px 12px;
            min-width: 40px;
            height: 40px;
            font-size: 14px;
          }
        }
      `}</style>
    </div>
  );
};

export default Pagination;
