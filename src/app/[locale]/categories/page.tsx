import React from 'react';
import Navbar from '@/components/Layout/Navbar';
import Footer from '@/components/Layout/Footer';
import PageBanner from '@/components/Common/PageBanner';
import CategoryGrid from '@/components/Blog/CategoryGrid';
import Pagination from '@/components/Common/Pagination';
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getLocale } from 'next-intl/server';
import { generateMetadataTitle } from '@/utils/seo';
import { categoryApi } from '@/api/blog/category';
import { TCategoryResponse } from '@/types/blog/category';
import { notFound } from 'next/navigation';

interface CategoriesPageProps {
  searchParams: Promise<{
    page?: string;
    per_page?: string;
  }>;
}

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale();
  const t = await getTranslations('pages.categories');
  const url = `https://cslant.com/${locale !== 'en' ? locale + '/' : ''}categories`;
  const title = t('title', { default: 'Categories | CSlant' });
  const description = t('description', { default: 'Browse all categories and topics on CSlant.' });
  const imageUrl = 'https://cslant.com/images/og-categories.jpg';

  const titleConfig = {
    title: title,
    template: '%s | CSlant'
  };

  return {
    ...generateMetadataTitle(titleConfig),
    description: description,
    creator: 'CSlant',
    authors: [{ name: 'CSlant', url: 'https://cslant.com' }],
    publisher: 'CSlant',
    alternates: {
      canonical: 'https://cslant.com/categories',
      languages: {
        en: 'https://cslant.com/categories',
        vi: 'https://cslant.com/vi/categories'
      }
    },
    openGraph: {
      title: title,
      description: description,
      url: url,
      siteName: 'CSlant',
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: locale === 'vi' ? 'vi_VN' : 'en_US',
      type: 'website'
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
      images: [imageUrl],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1
      }
    }
  };
}

export default function Page() {
  const t = useTranslations('pages.blog');
  const fetchAllCategories = async () => {
    const response = await categoryApi.get({
      params: {
        page: 1,
        per_page: 10
      }
    });
    const blogPost = response.data;
  };
  React.useEffect(() => {
    fetchAllCategories();
  }, []);
  return (
    <>
      <Navbar />

      <PageBanner pageTitle={t('title')} />

      <BlogGridPost />

      <Footer />
    </>
  );
};
