import React from "react";
import { useTranslations } from 'next-intl';
import Navbar from "@/components/Layout/Navbar";
import Footer from "@/components/Layout/Footer";
import PageBanner from "@/components/Common/PageBanner";
import BlogGridPost from "@/components/Blog/BlogGridPost";
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getLocale } from 'next-intl/server';
import { generateMetadataTitle } from "@/utils/seo";

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale();
  const t = await getTranslations('pages.blog');
  const url = `https://cslant.com/${locale !== 'en' ? locale + '/' : ''}blog`;
  const title = t('title', { default: 'Blog | CSlant' });
  const description = t('description', { default: 'Read the latest news, insights, and updates from CSlant.' });
  const imageUrl = 'https://cslant.com/images/og-blog.jpg';

  const titleConfig = {
    title: title,
    template: '%s | CSlant'
  };

  return {
    ...generateMetadataTitle(titleConfig),
    description: description,
    creator: 'CSlant',
    authors: [{ name: 'CSlant', url: 'https://cslant.com' }],
    publisher: 'CSlant',
    alternates: {
      canonical: 'https://cslant.com/blog',
      languages: {
        en: 'https://cslant.com/blog',
        vi: 'https://cslant.com/vi/blog',
      },
    },
    openGraph: {
      title: title,
      description: description,
      url: url,
      siteName: 'CSlant',
      // images: [
      //   {
      //     url: imageUrl,
      //     width: 1200,
      //     height: 630,
      //     alt: title,
      //   },
      // ],
      locale: locale === 'vi' ? 'vi_VN' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
      // images: [imageUrl],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

  };
}

export default function Page() {
  const t = useTranslations('pages.blog');

  return (
    <>
      <Navbar />

      <PageBanner pageTitle={t('title')} />

      <BlogGridPost />

      <Footer />
    </>
  );
};
